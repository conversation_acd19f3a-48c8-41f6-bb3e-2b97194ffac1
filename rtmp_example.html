<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flowkar RTMP Streaming Example</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .info-box {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .warning-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        pre {
            background-color: #f1f1f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px 0;
        }
        button:hover {
            background-color: #2980b9;
        }
        input, select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
        }
        .result {
            margin-top: 20px;
        }
        #videoPlayer {
            width: 100%;
            background-color: #000;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <h1>Flowkar RTMP Streaming Example</h1>
    
    <div class="container">
        <h2>Generate RTMP URL</h2>
        <div>
            <label for="userId">User ID:</label>
            <input type="text" id="userId" placeholder="Enter user ID" value="12345">
        </div>
        <div>
            <label for="roomId">Room ID:</label>
            <input type="text" id="roomId" placeholder="Enter room ID" value="live-12345">
        </div>
        <button id="generateBtn">Generate RTMP URL</button>
        
        <div class="result" id="rtmpResult"></div>
    </div>
    
    <div class="container">
        <h2>Stream Information</h2>
        <div>
            <label for="userIdInfo">User ID:</label>
            <input type="text" id="userIdInfo" placeholder="Enter user ID" value="12345">
        </div>
        <button id="getInfoBtn">Get Stream Info</button>
        
        <div class="result" id="infoResult"></div>
    </div>
    
    <div class="container">
        <h2>Stop Stream</h2>
        <div>
            <label for="userIdStop">User ID:</label>
            <input type="text" id="userIdStop" placeholder="Enter user ID" value="12345">
        </div>
        <button id="stopStreamBtn">Stop Stream</button>
        
        <div class="result" id="stopResult"></div>
    </div>
    
    <div class="container">
        <h2>RTMP Server Status</h2>
        <button id="getStatusBtn">Get Server Status</button>
        
        <div class="result" id="statusResult"></div>
    </div>
    
    <div class="container">
        <h2>How to Use RTMP URL</h2>
        
        <h3>For Streamers (OBS Studio)</h3>
        <div class="info-box">
            <p>1. Open OBS Studio</p>
            <p>2. Go to Settings > Stream</p>
            <p>3. Select "Custom..." as the service</p>
            <p>4. For Server, enter: <code id="obsServer">rtmp://localhost:1935/live</code></p>
            <p>5. For Stream Key, enter: <code id="obsStreamKey">your-stream-key</code></p>
            <p>6. Click "OK" and then "Start Streaming"</p>
        </div>
        
        <h3>For Mobile Streamers</h3>
        <div class="info-box">
            <p>In your mobile streaming app, enter the full RTMP URL:</p>
            <code id="fullRtmpUrl">rtmp://localhost:1935/live/your-stream-key</code>
        </div>
        
        <h3>For Viewers (HLS)</h3>
        <div class="info-box">
            <p>Share this URL with viewers:</p>
            <code id="hlsUrl">http://localhost:8080/hls/your-stream-key.m3u8</code>
        </div>
    </div>
    
    <script>
        const BASE_URL = "http://localhost:30066"; // Your Flask app URL
        
        // Generate RTMP URL
        document.getElementById('generateBtn').addEventListener('click', async () => {
            const userId = document.getElementById('userId').value;
            const roomId = document.getElementById('roomId').value;
            const resultDiv = document.getElementById('rtmpResult');
            
            if (!userId || !roomId) {
                resultDiv.innerHTML = '<div class="warning-box">Please enter both User ID and Room ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Generating RTMP URL...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/rtmp/generate`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ user_id: userId, room_id: roomId })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const rtmpInfo = data.rtmp_info;
                    resultDiv.innerHTML = `
                        <div class="info-box">
                            <h3>RTMP URL Generated!</h3>
                            <p><strong>RTMP URL:</strong> ${rtmpInfo.rtmp_url}</p>
                            <p><strong>Stream Key:</strong> ${rtmpInfo.stream_key}</p>
                            <p><strong>Playback URL:</strong> ${rtmpInfo.playback_url}</p>
                            <p><strong>HLS URL:</strong> ${rtmpInfo.hls_url}</p>
                        </div>
                    `;
                    
                    // Update the example sections
                    document.getElementById('obsServer').textContent = `rtmp://${rtmpInfo.server_host}:${rtmpInfo.server_port}/${rtmpInfo.application}`;
                    document.getElementById('obsStreamKey').textContent = rtmpInfo.stream_key;
                    document.getElementById('fullRtmpUrl').textContent = rtmpInfo.rtmp_url;
                    document.getElementById('hlsUrl').textContent = rtmpInfo.hls_url;
                    
                    // Update other user ID fields
                    document.getElementById('userIdInfo').value = userId;
                    document.getElementById('userIdStop').value = userId;
                } else {
                    resultDiv.innerHTML = `<div class="warning-box">Failed to generate RTMP URL: ${JSON.stringify(data)}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="warning-box">Error: ${error.message}</div>`;
            }
        });
        
        // Get Stream Info
        document.getElementById('getInfoBtn').addEventListener('click', async () => {
            const userId = document.getElementById('userIdInfo').value;
            const resultDiv = document.getElementById('infoResult');
            
            if (!userId) {
                resultDiv.innerHTML = '<div class="warning-box">Please enter User ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Getting stream info...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/rtmp/info/${userId}`);
                const data = await response.json();
                
                if (data.success) {
                    const streamInfo = data.stream_info;
                    resultDiv.innerHTML = `
                        <div class="info-box">
                            <h3>Stream Info Retrieved!</h3>
                            <p><strong>RTMP URL:</strong> ${streamInfo.rtmp_url}</p>
                            <p><strong>Stream Key:</strong> ${streamInfo.stream_key}</p>
                            <p><strong>Room ID:</strong> ${streamInfo.room_id}</p>
                            <p><strong>Created At:</strong> ${streamInfo.created_at}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="warning-box">No active stream found for user ${userId}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="warning-box">Error: ${error.message}</div>`;
            }
        });
        
        // Stop Stream
        document.getElementById('stopStreamBtn').addEventListener('click', async () => {
            const userId = document.getElementById('userIdStop').value;
            const resultDiv = document.getElementById('stopResult');
            
            if (!userId) {
                resultDiv.innerHTML = '<div class="warning-box">Please enter User ID</div>';
                return;
            }
            
            resultDiv.innerHTML = 'Stopping stream...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/rtmp/stop/${userId}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="info-box">Stream stopped successfully for user ${userId}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="warning-box">No active stream found for user ${userId}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="warning-box">Error: ${error.message}</div>`;
            }
        });
        
        // Get RTMP Server Status
        document.getElementById('getStatusBtn').addEventListener('click', async () => {
            const resultDiv = document.getElementById('statusResult');
            
            resultDiv.innerHTML = 'Getting server status...';
            
            try {
                const response = await fetch(`${BASE_URL}/api/rtmp/status`);
                const data = await response.json();
                
                if (data.success) {
                    const config = data.rtmp_config;
                    resultDiv.innerHTML = `
                        <div class="info-box">
                            <h3>RTMP Server Status</h3>
                            <p><strong>Server:</strong> ${config.server_host}:${config.server_port}</p>
                            <p><strong>Application:</strong> ${config.application_name}</p>
                            <p><strong>Active Streams:</strong> ${data.active_streams}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="warning-box">Failed to get RTMP status</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="warning-box">Error: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>

# try:
#     import eventlet
#     eventlet.monkey_patch()
#     async_mode = 'eventlet'
# except ImportError:
#     async_mode = 'threading'

# import os
# import logging
# from typing import Dict, Set, Optional
# from flask import Flask, jsonify, request
# from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
# from flask_cors import CORS
# from datetime import datetime

# from db import *
# from helpers.email_regex import extract_after_hyphen

# # Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# logger = logging.getLogger(__name__)

# app = Flask(__name__)
# app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')

# # Configure CORS with specific origins
# ALLOWED_ORIGINS = [
#     "https://firecamp.dev",
#     "https://staging.flowkar.com",
#     "http://staging.flowkar.com",
#     "https://api.flowkar.com",
#     "http://localhost:3000",
#     "http://dev.flowkar.com",
#     "https://dev.flowkar.com",
#     "https://beta.flowkar.com",
#     "https://app.flowkar.com",
#     "https://live.flowkar.com",
# ]

# CORS(app, resources={r"/*": {"origins": ALLOWED_ORIGINS}})

# # Configure SocketIO with enhanced settings
# socketio = SocketIO(
#     app,
#     cors_allowed_origins=ALLOWED_ORIGINS,
#     max_http_buffer_size=1024*1024*400,  # 400MB buffer
#     ping_timeout=120,
#     ping_interval=25,
#     async_mode=async_mode
# )

# class RoomManager:
#     def __init__(self):
#         logger.info("Initializing RoomManager")
#         self.active_rooms = set()  # Track active rooms

#     def create_room(self, room_id: str, user_id: str) -> bool:
#         logger.info(f"Attempting to create room: {room_id} for user: {user_id}")
#         result = db_create_room(room_id, user_id)
#         if result:
#             self.active_rooms.add(room_id)
#         logger.info(f"Room creation {'successful' if result else 'failed'} for room: {room_id}")
#         return result

#     def join_room(self, room_id: str, user_id: str) -> bool:
#         logger.info(f"Attempting to join room: {room_id} by user: {user_id}")
#         result = db_join_room(room_id, user_id)
#         logger.info(f"Room join {'successful' if result else 'failed'} for room: {room_id}, user: {user_id}")
#         return result

#     def leave_room(self, room_id: str, user_id: str) -> bool:
#         logger.info(f"Attempting to leave room: {room_id} by user: {user_id}")
#         result = db_leave_room(room_id, user_id)
#         logger.info(f"Room leave {'successful' if result else 'failed'} for room: {room_id}, user: {user_id}")
#         return result

#     def close_room(self, room_id: str) -> bool:
#         logger.info(f"Attempting to close room: {room_id}")
#         result = db_close_room(room_id)
#         if result:
#             self.active_rooms.discard(room_id)
#         logger.info(f"Room close {'successful' if result else 'failed'} for room: {room_id}")
#         return result

#     def get_room_users(self, room_id: str) -> Set[str]:
#         logger.info(f"Fetching users for room: {room_id}")
#         users = set(db_get_room_users(room_id))
#         logger.info(f"Found {len(users)} users in room: {room_id}")
#         return users

#     def get_user_room(self, user_id: str) -> Optional[str]:
#         logger.info(f"Fetching room for user: {user_id}")
#         room = db_get_user_room(user_id)
#         logger.info(f"User {user_id} is {'in room: ' + room if room else 'not in any room'}")
#         return room

#     def room_exists(self, room_id: str) -> bool:
#         logger.info(f"Checking if room exists: {room_id}")
#         exists = db_room_exists(room_id)
#         logger.info(f"Room {room_id} {'exists' if exists else 'does not exist'}")
#         return exists

#     def is_room_active(self, room_id: str) -> bool:
#         return room_id in self.active_rooms

# room_manager = RoomManager()

# def send_current_users_count(room_id):
#     """Background task to send current users count to all users in a room"""
#     logger.info(f"Starting user count monitoring for room: {room_id}")
#     while True:
#         try:
#             if not room_manager.is_room_active(room_id):
#                 logger.info(f"Room {room_id} is no longer active, stopping user count monitoring")
#                 break
                
#             users = room_manager.get_room_users(room_id)
#             user_count = len(users)
#             logger.info(f"Emitting user count for room {room_id}: {user_count} users")
#             socketio.emit('current_users_number', {
#                 'room': room_id,
#                 'count': user_count
#             }, room=room_id)
#             eventlet.sleep(2)  # Sleep for 2 seconds
#         except Exception as e:
#             logger.error(f"Error sending users count for room {room_id}: {str(e)}")
#             break

# def send_room_comments(room_id):
#     """Background task to send comments to all users in a room"""
#     logger.info(f"Starting comment monitoring for room: {room_id}")
#     previous_comments = None  # Track previous comments
    
#     while True:
#         try:
#             if not room_manager.is_room_active(room_id):
#                 logger.info(f"Room {room_id} is no longer active, stopping comment monitoring")
#                 break
                
#             current_comments = db_get_room_comments(room_id)
            
#             # Only emit if comments have changed
#             if current_comments != previous_comments:
#                 logger.info(f"New comments detected, emitting {len(current_comments)} comments for room {room_id}")
#                 socketio.emit('comment_list', {
#                     'room': room_id,
#                     'comments': current_comments
#                 }, room=room_id)
#                 previous_comments = current_comments
#             else:
#                 logger.debug(f"No new comments for room {room_id}")
                
#             eventlet.sleep(7)  # Sleep for 7 seconds
#         except Exception as e:
#             logger.error(f"Error sending comments for room {room_id}: {str(e)}")
#             break

# @socketio.on('create_room')
# def handle_create_room(data):
#     try:
#         room_id = data['room']
#         user_id = extract_after_hyphen(room_id)
#         logger.info(f"Creating room: {room_id} by user {request.sid}")

#         is_user, user_obj = db_get_user(user_id)
#         if not is_user:
#             emit('room_created', {'error': 'User does not exist'}, status=404)
#             return

#         req_id = request.sid
#         db_update_user_live_id(user_id, req_id)

#         if room_manager.create_room(room_id, user_id):
#             join_room(room_id)
#             room_manager.join_room(room_id, request.sid)
#             # Start background tasks
#             eventlet.spawn(send_current_users_count, room_id)
#             eventlet.spawn(send_room_comments, room_id)
#             emit('room_created', {'room': room_id, 'status': 'success'})
#             logger.info(f"Room {room_id} created successfully")
#         else:
#             handle_join_room({'room': room_id})

#     except Exception as e:
#         logger.error(f"Error creating room: {str(e)}")
#         emit('room_created', {'error': 'Internal server error'}, status=500)

# @socketio.on('close_room')
# def handle_close_room(data):
#     try:
#         logger.info("=== Close Room Event Started ===")
#         logger.info(f"Received data: {data}")
#         room_id = data['room']
#         logger.info(f"Closing room: {room_id} by user {request.sid}")

#         if room_manager.room_exists(room_id):
#             logger.info(f"Room {room_id} exists, proceeding with close")
#             user_id = extract_after_hyphen(room_id)
#             logger.info(f"Extracted user_id: {user_id}")
            
#             db_update_user_live_id(user_id, '')
#             logger.info(f"Updated user live_id in database")
            
#             # Clear room comments
#             db_clear_room_comments(room_id)
#             logger.info(f"Cleared comments for room {room_id}")
            
#             room_manager.close_room(room_id)
#             logger.info(f"Room manager closed room {room_id}")
            
#             emit('room_closed', {
#                 'room': room_id,
#                 'message': 'Room closed successfully'
#             }, room=room_id)
#             logger.info(f"Emitted room_closed event to room {room_id}")
#             logger.info(f"Room {room_id} closed successfully")
#         else:
#             logger.warning(f"Room {room_id} does not exist")
#             emit('room_closed', {'error': 'Room does not exist'}, status=404)

#     except Exception as e:
#         logger.error(f"Error closing room: {str(e)}")
#         logger.error(f"Full error details: {e.__class__.__name__}: {str(e)}")
#         emit('room_closed', {'error': 'Internal server error'}, status=500)
#     finally:
#         logger.info("=== Close Room Event Ended ===")

# @socketio.on('leave_stream')
# def handle_leave_stream(data):
#     try:
#         room_id = data['room']
#         logger.info(f"User {request.sid} leaving room: {room_id}")

#         if room_manager.room_exists(room_id):
#             room_manager.leave_room(room_id, request.sid)
#             leave_room(room_id)
#             emit('leave_stream', {
#                 'status': 'success',
#                 'message': f'Left room {room_id} successfully'
#             })
#             logger.info(f"User {request.sid} left room {room_id}")
#         else:
#             emit('leave_stream', {'error': 'Room does not exist'}, status=404)

#     except Exception as e:
#         logger.error(f"Error leaving stream: {str(e)}")
#         emit('leave_stream', {'error': 'Internal server error'}, status=500)

# @socketio.on('check_room')
# def check_room(data):
#     try:
#         room_id = data.get('room')
#         exists = room_manager.room_exists(room_id)
#         logger.info(f"Checking room {room_id}: {'exists' if exists else 'does not exist'}")
#         emit('check_room', {'valid': exists})
#     except Exception as e:
#         logger.error(f"Error checking room: {str(e)}")
#         emit('check_room', {'error': 'Internal server error'}, status=500)

# @socketio.on('send_comment')
# def handle_send_comment(data):
#     try:
#         room_id = data.get('room')
#         comment = data.get('comment')
#         user_id = data.get('userId')
#         is_animated = data.get('is_animated')
#         username = data.get('username', 'Anonymous')  # Default to Anonymous if username not provided
        
#         if not all([room_id, comment, user_id]):
#             emit('error', {'error': 'Missing required fields'}, status=400)
#             return

#         if not room_manager.room_exists(room_id):
#             emit('error', {'error': 'Room does not exist'}, status=404)
#             return

#         # Store comment in database
#         if not db_add_comment(room_id, user_id, username, comment, is_animated):
#             emit('error', {'error': 'Failed to store comment'}, status=500)
#             return

#         # Create comment object with timestamp
#         comment_data = {
#             'room': room_id,
#             'userId': user_id,
#             'username': username,
#             'comment': comment,
#             'is_animated': is_animated,
#             'timestamp': datetime.utcnow().isoformat()
#         }

#         # Broadcast the comment to all users in the room
#         emit('new_comment', comment_data, room=room_id)
#         logger.info(f"Comment sent in room {room_id} by user {user_id}")

#     except Exception as e:
#         logger.error(f"Error handling comment: {str(e)}")
#         emit('error', {'error': 'Internal server error'}, status=500)

# @socketio.on('join_room')
# def handle_join_room(data):
#     try:
#         room_id = data.get('room')
#         username = data.get('username', 'Anonymous')  # Get username from join data
#         logger.info(f"User {request.sid} attempting to join room {room_id}")

#         # Check if user is in any other room and remove them
#         current_room = room_manager.get_user_room(request.sid)
#         if current_room:
#             logger.info(f"User {request.sid} is leaving room {current_room} to join {room_id}")
#             room_manager.leave_room(current_room, request.sid)
#             leave_room(current_room)
#             # Notify other users in the previous room
#             emit('user_left', {
#                 'userId': request.sid,
#                 'room': current_room
#             }, room=current_room)

#         if room_manager.room_exists(room_id):
#             join_room(room_id)
#             room_manager.join_room(room_id, request.sid)
            
#             # Start background tasks for both user count and comments
#             eventlet.spawn(send_current_users_count, room_id)
#             eventlet.spawn(send_room_comments, room_id)
            
#             # Get and send existing comments to the joining user
#             comments = db_get_room_comments(room_id)
#             logger.info(f"Sent {len(comments)} existing comments to user {request.sid}")
            
#             # Notify other users in the room
#             for user_id in room_manager.get_room_users(room_id):
#                 if user_id != request.sid:
#                     emit('user_joined', {
#                         'userId': request.sid,
#                         'username': username,
#                         'room': room_id
#                     }, room=user_id)
            
#             logger.info(f"User {request.sid} joined room {room_id}")
#         else:
#             logger.warning(f"Attempt to join non-existent room {room_id}")
#             emit('error', {'error': 'Room does not exist'}, status=404)

#     except Exception as e:
#         logger.error(f"Error joining room: {str(e)}")
#         emit('error', {'error': 'Internal server error'}, status=500)

# @socketio.on('offer')
# def handle_offer(data):
#     try:
#         # Check if data is empty or missing required fields
#         if not data or not data.get('offer') or not data.get('userId') or not data.get('room'):
#             logger.warning(f"Empty or invalid offer data received from {request.sid}")
#             # Close the room if no valid offer data
#             room_id = data.get('room') if data else None
#             if room_id and room_manager.room_exists(room_id):
#                 logger.info(f"Closing room {room_id} due to invalid offer data")
#                 handle_close_room({'room': room_id})
#             return
            
#         logger.info(f"Handling offer from {request.sid} to {data['userId']} in room {data['room']}")
#         emit('offer', {
#             'offer': data['offer'],
#             'userId': request.sid,
#             'room': data['room']
#         }, room=data['userId'])
#     except Exception as e:
#         logger.error(f"Error handling offer: {str(e)}")
#         emit('error', {'error': 'Internal server error'}, status=500)

# @socketio.on('answer')
# def handle_answer(data):
#     try:
#         logger.info(f"Handling answer from {request.sid} to {data['userId']} in room {data['room']}")
#         emit('answer', {
#             'answer': data['answer'],
#             'userId': request.sid,
#             'room': data['room']
#         }, room=data['userId'])
#     except Exception as e:
#         logger.error(f"Error handling answer: {str(e)}")
#         emit('error', {'error': 'Internal server error'}, status=500)

# @socketio.on('ice_candidate')
# def handle_ice_candidate(data):
#     try:
#         logger.info(f"Handling ICE candidate from {request.sid} to {data['userId']}")
#         emit('ice_candidate', {
#             'candidate': data['candidate'],
#             'userId': request.sid,
#             'room': data['room']
#         }, room=data['userId'])
#     except Exception as e:
#         logger.error(f"Error handling ICE candidate: {str(e)}")
#         emit('error', {'error': 'Internal server error'}, status=500)

# @socketio.on('live_disconnect')
# def handle_disconnect():
#     try:
#         user_room = room_manager.get_user_room(request.sid)
#         if user_room:
#             logger.info(f"User {request.sid} disconnecting from room {user_room}")
#             room_manager.leave_room(user_room, request.sid)
#             user_id = extract_after_hyphen(user_room)
#             disconnected = db_update_user_live_id(user_id, '')
            
#             emit('user_left', {
#                 'userId': request.sid,
#                 'room': user_room
#             }, room=user_room)
        
#             logger.info(f"User {request.sid} disconnected from room {user_room}")
#             logger.info('!!!!!!!!!Done!!!!!!!', user_room)
#             logger.info('!!!!!!!!! Disconnect Done!!!!!!!', disconnected)
#     except Exception as e:
#         logger.error(f"Error handling disconnect: {str(e)}")

# if __name__ == '__main__':
#     port = int(os.getenv('PORT', 30066))
#     with app.app_context():
#         socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=port) 


try:
    import eventlet
    eventlet.monkey_patch()
    async_mode = 'eventlet'
except ImportError:
    async_mode = 'threading'

import os
import logging
import time
from typing import Dict, Set, Optional
from flask import Flask, jsonify, request
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
from flask_cors import CORS
from datetime import datetime

from db import *
from helpers.email_regex import extract_after_hyphen

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')

# Configure CORS with specific origins
ALLOWED_ORIGINS = [
    "https://firecamp.dev",
    "https://staging.flowkar.com",
    "http://staging.flowkar.com",
    "https://api.flowkar.com",
    "http://localhost:3000",
    "http://dev.flowkar.com",
    "https://dev.flowkar.com",
    "https://beta.flowkar.com",
    "https://app.flowkar.com",
    "https://live.flowkar.com",
]

CORS(app, resources={r"/*": {"origins": ALLOWED_ORIGINS}})

# Configure SocketIO with enhanced settings
socketio = SocketIO(
    app,
    cors_allowed_origins=ALLOWED_ORIGINS,
    max_http_buffer_size=1024*1024*400,  # 400MB buffer
    ping_timeout=120,
    ping_interval=25,
    async_mode=async_mode
)

class HeartbeatManager:
    def __init__(self):
        self.user_heartbeats = {}  # {user_id: last_heartbeat_time}
        self.room_heartbeats = {}  # {room_id: last_heartbeat_time}
        self.heartbeat_timeout = 15  # seconds
        self.monitoring_active = True
        logger.info("HeartbeatManager initialized")

    def update_user_heartbeat(self, user_id: str):
        """Update the last heartbeat time for a user"""
        current_time = time.time()
        self.user_heartbeats[user_id] = current_time
        logger.debug(f"Updated heartbeat for user {user_id}")

    def update_room_heartbeat(self, room_id: str):
        """Update the last heartbeat time for a room"""
        current_time = time.time()
        self.room_heartbeats[room_id] = current_time
        logger.debug(f"Updated heartbeat for room {room_id}")

    def remove_user_heartbeat(self, user_id: str):
        """Remove user from heartbeat tracking"""
        if user_id in self.user_heartbeats:
            del self.user_heartbeats[user_id]
            logger.info(f"Removed heartbeat tracking for user {user_id}")

    def remove_room_heartbeat(self, room_id: str):
        """Remove room from heartbeat tracking"""
        if room_id in self.room_heartbeats:
            del self.room_heartbeats[room_id]
            logger.info(f"Removed heartbeat tracking for room {room_id}")

    def is_user_alive(self, user_id: str) -> bool:
        """Check if user is still alive based on heartbeat"""
        if user_id not in self.user_heartbeats:
            return False
        
        last_heartbeat = self.user_heartbeats[user_id]
        current_time = time.time()
        return (current_time - last_heartbeat) <= self.heartbeat_timeout

    def is_room_alive(self, room_id: str) -> bool:
        """Check if room is still alive based on heartbeat"""
        if room_id not in self.room_heartbeats:
            return False
        
        last_heartbeat = self.room_heartbeats[room_id]
        current_time = time.time()
        return (current_time - last_heartbeat) <= self.heartbeat_timeout

    def get_dead_users(self) -> list:
        """Get list of users who haven't sent heartbeat in timeout period"""
        current_time = time.time()
        dead_users = []
        
        for user_id, last_heartbeat in self.user_heartbeats.items():
            if (current_time - last_heartbeat) > self.heartbeat_timeout:
                dead_users.append(user_id)
        
        return dead_users

    def get_dead_rooms(self) -> list:
        """Get list of rooms that haven't received heartbeat in timeout period"""
        current_time = time.time()
        dead_rooms = []
        
        for room_id, last_heartbeat in self.room_heartbeats.items():
            if (current_time - last_heartbeat) > self.heartbeat_timeout:
                dead_rooms.append(room_id)
        
        return dead_rooms

    def stop_monitoring(self):
        """Stop heartbeat monitoring"""
        self.monitoring_active = False
        logger.info("Heartbeat monitoring stopped")

class RoomManager:
    def __init__(self):
        logger.info("Initializing RoomManager")
        self.active_rooms = set()  # Track active rooms
        self.room_tasks = {}  # Track background tasks for each room
        self.user_rooms = {}  # Track which room each user is in

    def create_room(self, room_id: str, user_id: str) -> bool:
        logger.info(f"Attempting to create room: {room_id} for user: {user_id}")
        result = db_create_room(room_id, user_id)
        if result:
            self.active_rooms.add(room_id)
            self.room_tasks[room_id] = {'user_count': None, 'comments': None}
        logger.info(f"Room creation {'successful' if result else 'failed'} for room: {room_id}")
        return result

    def join_room(self, room_id: str, user_id: str) -> bool:
        logger.info(f"Attempting to join room: {room_id} by user: {user_id}")
        result = db_join_room(room_id, user_id)
        if result:
            self.user_rooms[user_id] = room_id
        logger.info(f"Room join {'successful' if result else 'failed'} for room: {room_id}, user: {user_id}")
        return result

    def leave_room(self, room_id: str, user_id: str) -> bool:
        logger.info(f"Attempting to leave room: {room_id} by user: {user_id}")
        result = db_leave_room(room_id, user_id)
        if result and user_id in self.user_rooms:
            del self.user_rooms[user_id]
        logger.info(f"Room leave {'successful' if result else 'failed'} for room: {room_id}, user: {user_id}")
        return result

    def close_room(self, room_id: str) -> bool:
        logger.info(f"Attempting to close room: {room_id}")
        result = db_close_room(room_id)
        if result:
            self.active_rooms.discard(room_id)
            # Clean up room tasks
            if room_id in self.room_tasks:
                del self.room_tasks[room_id]
            # Remove users from this room
            users_to_remove = [user_id for user_id, room in self.user_rooms.items() if room == room_id]
            for user_id in users_to_remove:
                del self.user_rooms[user_id]
        logger.info(f"Room close {'successful' if result else 'failed'} for room: {room_id}")
        return result

    def get_room_users(self, room_id: str) -> Set[str]:
        logger.info(f"Fetching users for room: {room_id}")
        users = set(db_get_room_users(room_id))
        logger.info(f"Found {len(users)} users in room: {room_id}")
        return users

    def get_user_room(self, user_id: str) -> Optional[str]:
        logger.info(f"Fetching room for user: {user_id}")
        room = self.user_rooms.get(user_id) or db_get_user_room(user_id)
        logger.info(f"User {user_id} is {'in room: ' + room if room else 'not in any room'}")
        return room

    def room_exists(self, room_id: str) -> bool:
        logger.info(f"Checking if room exists: {room_id}")
        exists = db_room_exists(room_id)
        logger.info(f"Room {room_id} {'exists' if exists else 'does not exist'}")
        return exists

    def is_room_active(self, room_id: str) -> bool:
        return room_id in self.active_rooms

    def cleanup_dead_room(self, room_id: str):
        """Clean up a dead room"""
        logger.info(f"Cleaning up dead room: {room_id}")
        
        # Get all users in the room
        users = self.get_room_users(room_id)
        
        # Notify all users that room is being closed
        for user_id in users:
            socketio.emit('room_force_closed', {
                'room': room_id,
                'reason': 'Room inactive - no heartbeat received'
            }, room=user_id)
        
        # Update database
        user_id = extract_after_hyphen(room_id)
        db_update_user_live_id(user_id, '')
        db_clear_room_comments(room_id)
        
        # Close the room
        self.close_room(room_id)
        
        # Remove from heartbeat tracking
        heartbeat_manager.remove_room_heartbeat(room_id)
        
        logger.info(f"Dead room {room_id} cleaned up successfully")

room_manager = RoomManager()
heartbeat_manager = HeartbeatManager()

def heartbeat_monitor():
    """Background task to monitor heartbeats and clean up dead connections"""
    logger.info("Starting heartbeat monitor")
    
    while heartbeat_manager.monitoring_active:
        try:
            # Check for dead rooms
            dead_rooms = heartbeat_manager.get_dead_rooms()
            for room_id in dead_rooms:
                logger.warning(f"Dead room detected: {room_id}")
                room_manager.cleanup_dead_room(room_id)
            
            # Check for dead users
            dead_users = heartbeat_manager.get_dead_users()
            for user_id in dead_users:
                logger.warning(f"Dead user detected: {user_id}")
                
                # Find user's room and handle cleanup
                user_room = room_manager.get_user_room(user_id)
                if user_room:
                    # Remove user from room
                    room_manager.leave_room(user_room, user_id)
                    
                    # Notify other users in the room
                    socketio.emit('user_disconnected', {
                        'userId': user_id,
                        'room': user_room,
                        'reason': 'No heartbeat received'
                    }, room=user_room)
                
                # Remove from heartbeat tracking
                heartbeat_manager.remove_user_heartbeat(user_id)
            
            eventlet.sleep(5)  # Check every 5 seconds
            
        except Exception as e:
            logger.error(f"Error in heartbeat monitor: {str(e)}")
            eventlet.sleep(5)
    
    logger.info("Heartbeat monitor stopped")

def send_current_users_count(room_id):
    """Background task to send current users count to all users in a room"""
    logger.info(f"Starting user count monitoring for room: {room_id}")
    
    while True:
        try:
            # Check if room is still alive
            if not room_manager.is_room_active(room_id) or not heartbeat_manager.is_room_alive(room_id):
                logger.info(f"Room {room_id} is no longer active or alive, stopping user count monitoring")
                break
                
            users = room_manager.get_room_users(room_id)
            user_count = len(users)
            logger.debug(f"Emitting user count for room {room_id}: {user_count} users")
            
            socketio.emit('current_users_number', {
                'room': room_id,
                'count': user_count
            }, room=room_id)
            
            eventlet.sleep(2)  # Sleep for 2 seconds
            
        except Exception as e:
            logger.error(f"Error sending users count for room {room_id}: {str(e)}")
            break
    
    logger.info(f"User count monitoring stopped for room: {room_id}")

def send_room_comments(room_id):
    """Background task to send comments to all users in a room"""
    logger.info(f"Starting comment monitoring for room: {room_id}")
    previous_comments = None
    
    while True:
        try:
            # Check if room is still alive
            if not room_manager.is_room_active(room_id) or not heartbeat_manager.is_room_alive(room_id):
                logger.info(f"Room {room_id} is no longer active or alive, stopping comment monitoring")
                break
                
            current_comments = db_get_room_comments(room_id)
            
            # Only emit if comments have changed
            if current_comments != previous_comments:
                logger.debug(f"New comments detected, emitting {len(current_comments)} comments for room {room_id}")
                socketio.emit('comment_list', {
                    'room': room_id,
                    'comments': current_comments
                }, room=room_id)
                previous_comments = current_comments
                
            eventlet.sleep(7)  # Sleep for 7 seconds
            
        except Exception as e:
            logger.error(f"Error sending comments for room {room_id}: {str(e)}")
            break
    
    logger.info(f"Comment monitoring stopped for room: {room_id}")

# Start heartbeat monitor
eventlet.spawn(heartbeat_monitor)

@socketio.on('heartbeat')
def handle_heartbeat(data):
    """Handle heartbeat from client"""
    try:
        user_id = data.get('userId', request.sid)
        room_id = data.get('room')
        
        # Update heartbeats
        heartbeat_manager.update_user_heartbeat(user_id)
        if room_id:
            heartbeat_manager.update_room_heartbeat(room_id)
        
        # Send heartbeat response
        emit('heartbeat_response', {
            'timestamp': datetime.utcnow().isoformat(),
            'status': 'alive'
        })
        
        logger.debug(f"Heartbeat received from user {user_id} in room {room_id}")
        
    except Exception as e:
        logger.error(f"Error handling heartbeat: {str(e)}")

@socketio.on('create_room')
def handle_create_room(data):
    try:
        room_id = data['room']
        user_id = extract_after_hyphen(room_id)
        logger.info(f"Creating room: {room_id} by user {request.sid}")

        is_user, user_obj = db_get_user(user_id)
        if not is_user:
            emit('room_created', {'error': 'User does not exist'}, status=404)
            return

        req_id = request.sid
        db_update_user_live_id(user_id, req_id)

        if room_manager.create_room(room_id, user_id):
            join_room(room_id)
            room_manager.join_room(room_id, request.sid)
            
            # Initialize heartbeat tracking
            heartbeat_manager.update_user_heartbeat(request.sid)
            heartbeat_manager.update_room_heartbeat(room_id)
            
            # Start background tasks
            eventlet.spawn(send_current_users_count, room_id)
            eventlet.spawn(send_room_comments, room_id)
            
            emit('room_created', {
                'room': room_id, 
                'status': 'success',
                'heartbeat_interval': 10  # Tell client to send heartbeat every 10 seconds
            })
            logger.info(f"Room {room_id} created successfully")
        else:
            handle_join_room({'room': room_id})

    except Exception as e:
        logger.error(f"Error creating room: {str(e)}")
        emit('room_created', {'error': 'Internal server error'}, status=500)

@socketio.on('close_room')
def handle_close_room(data):
    try:
        logger.info("=== Close Room Event Started ===")
        room_id = data['room']
        logger.info(f"Closing room: {room_id} by user {request.sid}")

        if room_manager.room_exists(room_id):
            user_id = extract_after_hyphen(room_id)
            
            # Remove from heartbeat tracking
            heartbeat_manager.remove_room_heartbeat(room_id)
            heartbeat_manager.remove_user_heartbeat(request.sid)
            
            db_update_user_live_id(user_id, '')
            db_clear_room_comments(room_id)
            room_manager.close_room(room_id)
            
            emit('room_closed', {
                'room': room_id,
                'message': 'Room closed successfully'
            }, room=room_id)
            
            logger.info(f"Room {room_id} closed successfully")
        else:
            emit('room_closed', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error closing room: {str(e)}")
        emit('room_closed', {'error': 'Internal server error'}, status=500)

@socketio.on('leave_stream')
def handle_leave_stream(data):
    try:
        room_id = data['room']
        logger.info(f"User {request.sid} leaving room: {room_id}")

        if room_manager.room_exists(room_id):
            room_manager.leave_room(room_id, request.sid)
            leave_room(room_id)
            
            # Remove from heartbeat tracking
            heartbeat_manager.remove_user_heartbeat(request.sid)
            
            emit('leave_stream', {
                'status': 'success',
                'message': f'Left room {room_id} successfully'
            })
            logger.info(f"User {request.sid} left room {room_id}")
        else:
            emit('leave_stream', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error leaving stream: {str(e)}")
        emit('leave_stream', {'error': 'Internal server error'}, status=500)

@socketio.on('check_room')
def check_room(data):
    try:
        room_id = data.get('room')
        exists = room_manager.room_exists(room_id)
        logger.info(f"Checking room {room_id}: {'exists' if exists else 'does not exist'}")
        emit('check_room', {'valid': exists})
    except Exception as e:
        logger.error(f"Error checking room: {str(e)}")
        emit('check_room', {'error': 'Internal server error'}, status=500)

@socketio.on('send_comment')
def handle_send_comment(data):
    try:
        room_id = data.get('room')
        comment = data.get('comment')
        user_id = data.get('userId')
        is_animated = data.get('is_animated')
        username = data.get('username', 'Anonymous')
        
        # Update heartbeat when user sends comment
        heartbeat_manager.update_user_heartbeat(user_id)
        heartbeat_manager.update_room_heartbeat(room_id)
        
        if not all([room_id, comment, user_id]):
            emit('error', {'error': 'Missing required fields'}, status=400)
            return

        if not room_manager.room_exists(room_id):
            emit('error', {'error': 'Room does not exist'}, status=404)
            return

        if not db_add_comment(room_id, user_id, username, comment, is_animated):
            emit('error', {'error': 'Failed to store comment'}, status=500)
            return

        comment_data = {
            'room': room_id,
            'userId': user_id,
            'username': username,
            'comment': comment,
            'is_animated': is_animated,
            'timestamp': datetime.utcnow().isoformat()
        }

        emit('new_comment', comment_data, room=room_id)
        logger.info(f"Comment sent in room {room_id} by user {user_id}")

    except Exception as e:
        logger.error(f"Error handling comment: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('join_room')
def handle_join_room(data):
    try:
        room_id = data.get('room')
        username = data.get('username', 'Anonymous')
        logger.info(f"User {request.sid} attempting to join room {room_id}")

        # Check if user is in any other room and remove them
        current_room = room_manager.get_user_room(request.sid)
        if current_room:
            logger.info(f"User {request.sid} is leaving room {current_room} to join {room_id}")
            room_manager.leave_room(current_room, request.sid)
            leave_room(current_room)
            emit('user_left', {
                'userId': request.sid,
                'room': current_room
            }, room=current_room)

        if room_manager.room_exists(room_id):
            join_room(room_id)
            room_manager.join_room(room_id, request.sid)
            
            # Initialize heartbeat tracking
            heartbeat_manager.update_user_heartbeat(request.sid)
            heartbeat_manager.update_room_heartbeat(room_id)
            
            # Start background tasks
            eventlet.spawn(send_current_users_count, room_id)
            eventlet.spawn(send_room_comments, room_id)
            
            comments = db_get_room_comments(room_id)
            
            for user_id in room_manager.get_room_users(room_id):
                if user_id != request.sid:
                    emit('user_joined', {
                        'userId': request.sid,
                        'username': username,
                        'room': room_id
                    }, room=user_id)
            
            emit('room_joined', {
                'room': room_id,
                'status': 'success',
                'heartbeat_interval': 10  # Tell client to send heartbeat every 10 seconds
            })
            
            logger.info(f"User {request.sid} joined room {room_id}")
        else:
            emit('error', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error joining room: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('offer')
def handle_offer(data):
    try:
        # Update heartbeat when offer is sent
        if data.get('userId') and data.get('room'):
            heartbeat_manager.update_user_heartbeat(request.sid)
            heartbeat_manager.update_room_heartbeat(data['room'])
        
        if not data or not data.get('offer') or not data.get('userId') or not data.get('room'):
            logger.warning(f"Empty or invalid offer data received from {request.sid}")
            room_id = data.get('room') if data else None
            if room_id and room_manager.room_exists(room_id):
                logger.info(f"Closing room {room_id} due to invalid offer data")
                handle_close_room({'room': room_id})
            return
            
        logger.info(f"Handling offer from {request.sid} to {data['userId']} in room {data['room']}")
        emit('offer', {
            'offer': data['offer'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling offer: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('answer')
def handle_answer(data):
    try:
        # Update heartbeat when answer is sent
        if data.get('room'):
            heartbeat_manager.update_user_heartbeat(request.sid)
            heartbeat_manager.update_room_heartbeat(data['room'])
        
        logger.info(f"Handling answer from {request.sid} to {data['userId']} in room {data['room']}")
        emit('answer', {
            'answer': data['answer'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling answer: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('ice_candidate')
def handle_ice_candidate(data):
    try:
        # Update heartbeat when ICE candidate is sent
        if data.get('room'):
            heartbeat_manager.update_user_heartbeat(request.sid)
            heartbeat_manager.update_room_heartbeat(data['room'])
        
        logger.info(f"Handling ICE candidate from {request.sid} to {data['userId']}")
        emit('ice_candidate', {
            'candidate': data['candidate'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling ICE candidate: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('live_disconnect')
def handle_disconnect():
    try:
        user_room = room_manager.get_user_room(request.sid)
        if user_room:
            logger.info(f"User {request.sid} disconnecting from room {user_room}")
            room_manager.leave_room(user_room, request.sid)
            user_id = extract_after_hyphen(user_room)
            db_update_user_live_id(user_id, '')
            
            # Remove from heartbeat tracking
            heartbeat_manager.remove_user_heartbeat(request.sid)
            
            emit('user_left', {
                'userId': request.sid,
                'room': user_room
            }, room=user_room)
        
            logger.info(f"User {request.sid} disconnected from room {user_room}")
    except Exception as e:
        logger.error(f"Error handling disconnect: {str(e)}")

if __name__ == '__main__':
    port = int(os.getenv('PORT', 30066))
    with app.app_context():
        socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=port)
import pymongo
from pymongo import MongoClient
from pymongo.server_api import Server<PERSON><PERSON>
from pymongo.errors import Duplicate<PERSON>eyError
from datetime import datetime

MONGO_URL = 'mongodb://Flowkar:Dgl14tP%3Dz%5DG%5E%3D-a1%40-5Che8_%24@157.173.218.248:27017/admin'

client = MongoClient(MONGO_URL, server_api=ServerApi('1'))

try:
    client.admin.command('ping')
    print("Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)


# Access the database and collection
db = client.db_flowkar_prod
LiveStreamingCollection = db.LiveStreaming
UserRegistrationCollection = db.UserRegistration

# Room Management Collections
RoomsCollection = db.Rooms
RoomMetadataCollection = db.RoomMetadata
UserRoomsCollection = db.UserRooms
CommentsCollection = db.Comments  # New collection for storing comments

def db_get_user(pk):
    user = UserRegistrationCollection.find_one({"_id": int(pk)})
    if user is None:
        return False,{}
    return True,user

def db_update_user_live_id(pk, live_id):
    update = UserRegistrationCollection.update_one({"_id": int(pk)}, {"$set": {"live_socket_id": live_id}})
    return True

def db_create_room(room_id: str, user_id: str) -> bool:
    """Create a new room in MongoDB"""
    try:
        # Check if room already exists
        if RoomsCollection.find_one({"_id": room_id}):
            return False
            
        # Create room document
        room_doc = {
            "_id": room_id,
            "users": [user_id],
            "created_at": datetime.utcnow()
        }
        
        # Create room metadata
        metadata_doc = {
            "_id": room_id,
            "created_at": datetime.utcnow(),
            "creator": user_id,
            "last_activity": datetime.utcnow()
        }
        
        # Create user-room mapping
        user_room_doc = {
            "_id": user_id,
            "room_id": room_id
        }
        
        # Insert all documents
        RoomsCollection.insert_one(room_doc)
        RoomMetadataCollection.insert_one(metadata_doc)
        UserRoomsCollection.insert_one(user_room_doc)
        
        return True
    except Exception as e:
        print(f"Error creating room: {str(e)}")
        return False

def db_join_room(room_id: str, user_id: str) -> bool:
    """Add a user to a room"""
    try:
        # Check if room exists
        if not RoomsCollection.find_one({"_id": room_id}):
            return False
            
        # Add user to room
        RoomsCollection.update_one(
            {"_id": room_id},
            {"$addToSet": {"users": user_id}}
        )
        
        # Update room metadata
        RoomMetadataCollection.update_one(
            {"_id": room_id},
            {"$set": {"last_activity": datetime.utcnow()}}
        )
        
        # Update user-room mapping
        UserRoomsCollection.update_one(
            {"_id": user_id},
            {"$set": {"room_id": room_id}},
            upsert=True
        )
        
        return True
    except Exception as e:
        print(f"Error joining room: {str(e)}")
        return False

def db_leave_room(room_id: str, user_id: str) -> bool:
    """Remove a user from a room"""
    try:
        # Get room metadata to check if leaving user is the creator
        room_metadata = RoomMetadataCollection.find_one({"_id": room_id})
        is_creator = room_metadata and room_metadata.get("creator") == user_id

        # Remove user from room
        RoomsCollection.update_one(
            {"_id": room_id},
            {"$pull": {"users": user_id}}
        )
        
        # Remove user-room mapping
        UserRoomsCollection.delete_one({"_id": user_id})
        
        # Only close room if the creator is leaving
        if is_creator:
            db_close_room(room_id)
            
        return True
    except Exception as e:
        print(f"Error leaving room: {str(e)}")
        return False

def db_close_room(room_id: str) -> bool:
    """Close a room and remove all associated data"""
    try:
        # Get all users in the room
        room = RoomsCollection.find_one({"_id": room_id})
        if room:
            users = room.get("users", [])
            
            # Remove user-room mappings for all users
            for user_id in users:
                UserRoomsCollection.delete_one({"_id": user_id})
        
        # Remove room and metadata
        RoomsCollection.delete_one({"_id": room_id})
        RoomMetadataCollection.delete_one({"_id": room_id})
        
        return True
    except Exception as e:
        print(f"Error closing room: {str(e)}")
        return False

def db_get_room_users(room_id: str) -> list:
    """Get all users in a room"""
    try:
        room = RoomsCollection.find_one({"_id": room_id})
        return room.get("users", []) if room else []
    except Exception as e:
        print(f"Error getting room users: {str(e)}")
        return []

def db_get_user_room(user_id: str) -> str:
    """Get the room ID for a user"""
    try:
        user_room = UserRoomsCollection.find_one({"_id": user_id})
        return user_room.get("room_id") if user_room else None
    except Exception as e:
        print(f"Error getting user room: {str(e)}")
        return None

def db_room_exists(room_id: str) -> bool:
    """Check if a room exists"""
    try:
        return bool(RoomsCollection.find_one({"_id": room_id}))
    except Exception as e:
        print(f"Error checking room existence: {str(e)}")
        return False

def db_add_comment(room_id: str, user_id: str, username: str, comment: str, is_animated: bool) -> bool:
    """Add a comment to a room"""
    try:
        comment_doc = {
            "room_id": room_id,
            "user_id": user_id,
            "username": username,
            "comment": comment,
            "is_animated": is_animated,
            "timestamp": datetime.utcnow()
        }
        CommentsCollection.insert_one(comment_doc)
        return True
    except Exception as e:
        print(f"Error adding comment: {str(e)}")
        return False

def db_get_room_comments(room_id: str) -> list:
    """Get all comments for a room"""
    try:
        comments = list(CommentsCollection.find(
            {"room_id": room_id},
            {"_id": 0}  # Exclude MongoDB _id field
        ).sort("timestamp", 1))  # Sort by timestamp ascending
        
        # Convert datetime objects to ISO format strings
        for comment in comments:
            if isinstance(comment.get('timestamp'), datetime):
                comment['timestamp'] = comment['timestamp'].isoformat()
        
        return comments
    except Exception as e:
        print(f"Error getting room comments: {str(e)}")
        return []

def db_clear_room_comments(room_id: str) -> bool:
    """Clear all comments for a room"""
    try:
        CommentsCollection.delete_many({"room_id": room_id})
        return True
    except Exception as e:
        print(f"Error clearing room comments: {str(e)}")
        return False

# import os
# from venv import logger
# from flask import Flask, jsonify, request
# from flask_socketio import So<PERSON><PERSON>, emit,  join_room, leave_room, disconnect
# from flask_cors import CORS


# from db import *
# from helpers.email_regex import extract_after_hyphen

# app = Flask(__name__)
# app.config['SECRET_KEY'] = 'your_secret_key'

# CORS(app, resources={r"/*": {"origins": "*"}})  # Allow firecamp.dev
# socketio = SocketIO(app, cors_allowed_origins=["https://firecamp.dev","https://staging.flowkar.com","http://staging.flowkar.com","https://api.flowkar.com","http://localhost:3000","http://dev.flowkar.com","https://dev.flowkar.com","https://beta.flowkar.com","https://app.flowkar.com"], max_http_buffer_size=1024*1024*400, ping_timeout=120, ping_interval=25)

# rooms = {}


# @socketio.on('create_room')
# def handle_create_room(data):
#     room_id = data['room']
#     logger.info(f"Creating room: {room_id} by user {request.sid}")
#     user_id = extract_after_hyphen(room_id)
#     print("Before Creating ",rooms)
    
#     is_user,user_obj = db_get_user(user_id)
#     if is_user:
#         req_id = request.sid
#         db_update_user_live_id(user_id, req_id)
#         if room_id not in rooms:
#             rooms[room_id] = set()
#             print(rooms)
#             join_room(room_id)
#             rooms[room_id].add(request.sid)
#             emit('room_created', {'room': room_id})
#             logger.info(f"Room {room_id} created successfully")
#         else:
#             logger.info(f"Room {room_id} already exists")
#             handle_join_room({'room': room_id})
#     else:
#         emit('room_created',{'message':"User does not exists."})

# @socketio.on('close_room')
# def handle_close_room(data):
#     room_id = data['room']
#     logger.info(f"Closing room: {room_id} by user {request.sid}")
    
#     if room_id in rooms:
#         leave_room(room_id)
#         del rooms[room_id]
#         print("Closing Rooms",rooms)
#         user_id = extract_after_hyphen(room_id)
#         db_update_user_live_id(user_id, '')
#         emit('room_closed', {'room': room_id, 'message': 'Room closed successfully'}, room=room_id)
#         logger.info(f"Room {room_id} closed successfully")
#     else:
#         emit('room_closed', {'message': 'Room does not exist'})

# @socketio.on('leave_stream')
# def handle_close_room(data):
#     room_id = data['room']
#     logger.info(f"Closing room: {room_id} by user {request.sid}")
    
#     if room_id in rooms:
#         print(f" Length Before User - {request.sid} Leaving = {len(rooms[room_id])}")
#         rooms[room_id].remove(request.sid)
#         leave_room(room_id)
#         print(f"Rooms In Leave_stream - {rooms[room_id]}")
#         print(f" Length After User - {request.sid} Leaving = {len(rooms[room_id])}")
#         print("Closing Rooms",rooms)
#         logger.info(f"User {request.sid} Left {room_id} room successfully")
#         print(f"User {request.sid} Left {room_id} room successfully")
#     else:
#         print("Issue in leaving Stream Socket")
#         emit('leave_stream', {'message': 'Room does not exist'})


# @socketio.on('check_room')
# def check_room(data):
#     room_id = data.get('room')
#     exists = room_id in rooms
#     logger.info(f"Checking room {room_id}: {'exists' if exists else 'does not exist'}")
#     emit('check_room', {'valid': exists})

# @socketio.on('join_room')
# def handle_join_room(data):
#     room_id = data.get('room')
#     logger.info(f"User {request.sid} attempting to join room {room_id}")
#     print(f"User {request.sid} attempting to join room {room_id}")
#     if room_id in rooms:
#         join_room(room_id)
#         rooms[room_id].add(request.sid)
#         logger.info(f"Current users in room {room_id}: {len(rooms[room_id])}")
#         print(f"{room_id}: {len(rooms[room_id])}")
#         print(f'User {request.sid} Joined Room - {room_id}')
#         for user_id in rooms[room_id]:
#             if user_id != request.sid:
#                 emit('user_joined', {
#                     'userId': request.sid,
#                     'room': room_id
#                 }, room=user_id)
#                 logger.info(f"Notified user {user_id} about new user {request.sid}")
#     else:
#         print(f"User {request.sid} Trying to Join Room {room_id} But Failed Check Issue")
#         logger.warning(f"Attempt to join non-existent room {room_id}")
#         emit('error', {'message': 'Room does not exist'})


# @socketio.on('offer')
# def handle_offer(data):
#     logger.info(f"Handling offer from {request.sid} to {data['userId']} in room {data['room']}")
#     emit('offer', {
#         'offer': data['offer'],
#         'userId': request.sid,
#         'room': data['room']
#     }, room=data['userId'])

# @socketio.on('answer')
# def handle_answer(data):
#     logger.info(f"Handling answer from {request.sid} to {data['userId']} in room {data['room']}")
#     emit('answer', {
#         'answer': data['answer'],
#         'userId': request.sid,
#         'room': data['room']
#     }, room=data['userId'])

# @socketio.on('ice_candidate')
# def handle_ice_candidate(data):
#     logger.info(f"Handling ICE candidate from {request.sid} to {data['userId']}")
#     emit('ice_candidate', {
#         'candidate': data['candidate'],
#         'userId': request.sid,
#         'room': data['room']
#     }, room=data['userId'])

# @socketio.on('live_disconnect')
# def handle_disconnect():
#     for room_id in list(rooms.keys()):  
#         if request.sid in rooms[room_id]:
#             logger.info(f"User {request.sid} disconnecting from room {room_id}")
#             rooms[room_id].remove(request.sid)
#             user_id = extract_after_hyphen(room_id)
#             db_update_user_live_id(user_id, '')
#             emit('user_left', {
#                 'userId': request.sid,
#                 'room': room_id
#             }, room=room_id)
#             if len(rooms[room_id]) == 0:
#                 logger.info(f"Room {room_id} is empty, removing it")
#                 del rooms[room_id]
#             break

# if __name__ == '__main__':
#     socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=30066)


try:
    import eventlet
    eventlet.monkey_patch()
    async_mode = 'eventlet'
except ImportError:
    async_mode = 'threading'

import os
import logging
from typing import Dict, Set, Optional
from flask import Flask, jsonify, request
from flask_socketio import SocketIO, emit, join_room, leave_room, disconnect
from flask_cors import CORS
from datetime import datetime

from db import *
from helpers.email_regex import extract_after_hyphen

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your_secret_key')

# Configure CORS with specific origins
ALLOWED_ORIGINS = [
    "https://firecamp.dev",
    "https://staging.flowkar.com",
    "http://staging.flowkar.com",
    "https://api.flowkar.com",
    "http://localhost:3000",
    "http://dev.flowkar.com",
    "https://dev.flowkar.com",
    "https://beta.flowkar.com",
    "https://app.flowkar.com"
]

CORS(app, resources={r"/*": {"origins": ALLOWED_ORIGINS}})

# Configure SocketIO with enhanced settings
socketio = SocketIO(
    app,
    cors_allowed_origins=ALLOWED_ORIGINS,
    max_http_buffer_size=1024*1024*400,  # 400MB buffer
    ping_timeout=120,
    ping_interval=25,
    async_mode=async_mode
)

class RoomManager:
    def __init__(self):
        self.rooms: Dict[str, Set[str]] = {}
        self.user_rooms: Dict[str, str] = {}  # Maps user_id to room_id
        self.room_metadata: Dict[str, dict] = {}  # Stores additional room information

    def create_room(self, room_id: str, user_id: str) -> bool:
        if room_id not in self.rooms:
            self.rooms[room_id] = set()
            self.room_metadata[room_id] = {
                'created_at': datetime.utcnow(),
                'creator': user_id,
                'last_activity': datetime.utcnow()
            }
            return True
        return False

    def join_room(self, room_id: str, user_id: str) -> bool:
        if room_id in self.rooms:
            self.rooms[room_id].add(user_id)
            self.user_rooms[user_id] = room_id
            self.room_metadata[room_id]['last_activity'] = datetime.utcnow()
            return True
        return False

    def leave_room(self, room_id: str, user_id: str) -> bool:
        if room_id in self.rooms and user_id in self.rooms[room_id]:
            self.rooms[room_id].remove(user_id)
            if user_id in self.user_rooms:
                del self.user_rooms[user_id]
            
            if len(self.rooms[room_id]) == 0:
                self.close_room(room_id)
            return True
        return False

    def close_room(self, room_id: str) -> bool:
        if room_id in self.rooms:
            del self.rooms[room_id]
            if room_id in self.room_metadata:
                del self.room_metadata[room_id]
            return True
        return False

    def get_room_users(self, room_id: str) -> Set[str]:
        return self.rooms.get(room_id, set())

    def get_user_room(self, user_id: str) -> Optional[str]:
        return self.user_rooms.get(user_id)

    def room_exists(self, room_id: str) -> bool:
        return room_id in self.rooms

room_manager = RoomManager()

@socketio.on('create_room')
def handle_create_room(data):
    try:
        room_id = data['room']
        user_id = extract_after_hyphen(room_id)
        logger.info(f"Creating room: {room_id} by user {request.sid}")

        is_user, user_obj = db_get_user(user_id)
        if not is_user:
            emit('room_created', {'error': 'User does not exist'}, status=404)
            return

        req_id = request.sid
        db_update_user_live_id(user_id, req_id)

        if room_manager.create_room(room_id, user_id):
            join_room(room_id)
            room_manager.join_room(room_id, request.sid)
            emit('room_created', {'room': room_id, 'status': 'success'})
            logger.info(f"Room {room_id} created successfully")
        else:
            handle_join_room({'room': room_id})

    except Exception as e:
        logger.error(f"Error creating room: {str(e)}")
        emit('room_created', {'error': 'Internal server error'}, status=500)

@socketio.on('close_room')
def handle_close_room(data):
    try:
        room_id = data['room']
        logger.info(f"Closing room: {room_id} by user {request.sid}")

        if room_manager.room_exists(room_id):
            user_id = extract_after_hyphen(room_id)
            db_update_user_live_id(user_id, '')
            room_manager.close_room(room_id)
            emit('room_closed', {
                'room': room_id,
                'message': 'Room closed successfully'
            }, room=room_id)
            logger.info(f"Room {room_id} closed successfully")
        else:
            emit('room_closed', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error closing room: {str(e)}")
        emit('room_closed', {'error': 'Internal server error'}, status=500)

@socketio.on('leave_stream')
def handle_leave_stream(data):
    try:
        room_id = data['room']
        logger.info(f"User {request.sid} leaving room: {room_id}")

        if room_manager.room_exists(room_id):
            room_manager.leave_room(room_id, request.sid)
            leave_room(room_id)
            emit('leave_stream', {
                'status': 'success',
                'message': f'Left room {room_id} successfully'
            })
            logger.info(f"User {request.sid} left room {room_id}")
        else:
            emit('leave_stream', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error leaving stream: {str(e)}")
        emit('leave_stream', {'error': 'Internal server error'}, status=500)

@socketio.on('check_room')
def check_room(data):
    try:
        room_id = data.get('room')
        exists = room_manager.room_exists(room_id)
        logger.info(f"Checking room {room_id}: {'exists' if exists else 'does not exist'}")
        emit('check_room', {'valid': exists})
    except Exception as e:
        logger.error(f"Error checking room: {str(e)}")
        emit('check_room', {'error': 'Internal server error'}, status=500)

@socketio.on('join_room')
def handle_join_room(data):
    try:
        room_id = data.get('room')
        logger.info(f"User {request.sid} attempting to join room {room_id}")

        if room_manager.room_exists(room_id):
            join_room(room_id)
            room_manager.join_room(room_id, request.sid)
            
            # Notify other users in the room
            for user_id in room_manager.get_room_users(room_id):
                if user_id != request.sid:
                    emit('user_joined', {
                        'userId': request.sid,
                        'room': room_id
                    }, room=user_id)
            
            logger.info(f"User {request.sid} joined room {room_id}")
        else:
            logger.warning(f"Attempt to join non-existent room {room_id}")
            emit('error', {'error': 'Room does not exist'}, status=404)

    except Exception as e:
        logger.error(f"Error joining room: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('offer')
def handle_offer(data):
    try:
        logger.info(f"Handling offer from {request.sid} to {data['userId']} in room {data['room']}")
        emit('offer', {
            'offer': data['offer'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling offer: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('answer')
def handle_answer(data):
    try:
        logger.info(f"Handling answer from {request.sid} to {data['userId']} in room {data['room']}")
        emit('answer', {
            'answer': data['answer'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling answer: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('ice_candidate')
def handle_ice_candidate(data):
    try:
        logger.info(f"Handling ICE candidate from {request.sid} to {data['userId']}")
        emit('ice_candidate', {
            'candidate': data['candidate'],
            'userId': request.sid,
            'room': data['room']
        }, room=data['userId'])
    except Exception as e:
        logger.error(f"Error handling ICE candidate: {str(e)}")
        emit('error', {'error': 'Internal server error'}, status=500)

@socketio.on('live_disconnect')
def handle_disconnect():
    try:
        user_room = room_manager.get_user_room(request.sid)
        if user_room:
            logger.info(f"User {request.sid} disconnecting from room {user_room}")
            room_manager.leave_room(user_room, request.sid)
            user_id = extract_after_hyphen(user_room)
            db_update_user_live_id(user_id, '')
            
            emit('user_left', {
                'userId': request.sid,
                'room': user_room
            }, room=user_room)
            
            logger.info(f"User {request.sid} disconnected from room {user_room}")
    except Exception as e:
        logger.error(f"Error handling disconnect: {str(e)}")

if __name__ == '__main__':
    port = int(os.getenv('PORT', 30066))
    with app.app_context():
        socketio.run(app, allow_unsafe_werkzeug=True, debug=True, port=port) 


        
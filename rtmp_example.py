#!/usr/bin/env python3
"""
Example script demonstrating RTMP URL generation for Flowkar Live Streaming

This script shows how to:
1. Generate RTMP URLs for streaming
2. Get stream information
3. Stop streams
4. Check RTMP server status
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:30066"  # Your Flask app URL

def generate_rtmp_url(user_id, room_id):
    """Generate RTMP URL for a user and room"""
    url = f"{BASE_URL}/api/rtmp/generate"
    data = {
        "user_id": user_id,
        "room_id": room_id
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                rtmp_info = result['rtmp_info']
                print(f"✅ RTMP URL generated successfully!")
                print(f"📺 RTMP URL: {rtmp_info['rtmp_url']}")
                print(f"🔑 Stream Key: {rtmp_info['stream_key']}")
                print(f"📱 Playback URL: {rtmp_info['playback_url']}")
                print(f"🎬 HLS URL: {rtmp_info['hls_url']}")
                return rtmp_info
            else:
                print(f"❌ Failed to generate RTMP URL: {result}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def get_stream_info(user_id):
    """Get existing stream information for a user"""
    url = f"{BASE_URL}/api/rtmp/info/{user_id}"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                stream_info = result['stream_info']
                print(f"✅ Stream info retrieved!")
                print(f"📺 RTMP URL: {stream_info['rtmp_url']}")
                print(f"🔑 Stream Key: {stream_info['stream_key']}")
                print(f"🏠 Room ID: {stream_info['room_id']}")
                print(f"⏰ Created: {stream_info['created_at']}")
                return stream_info
            else:
                print(f"ℹ️ No active stream found for user {user_id}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def stop_stream(user_id):
    """Stop RTMP stream for a user"""
    url = f"{BASE_URL}/api/rtmp/stop/{user_id}"
    
    try:
        response = requests.delete(url)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"✅ Stream stopped successfully for user {user_id}")
                return True
            else:
                print(f"ℹ️ No active stream found for user {user_id}")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return False

def get_rtmp_status():
    """Get RTMP server status"""
    url = f"{BASE_URL}/api/rtmp/status"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                config = result['rtmp_config']
                print(f"✅ RTMP Server Status:")
                print(f"🖥️  Server: {config['server_host']}:{config['server_port']}")
                print(f"📁 Application: {config['application_name']}")
                print(f"📊 Active Streams: {result['active_streams']}")
                return result
            else:
                print(f"❌ Failed to get RTMP status")
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def main():
    """Example usage of RTMP URL generation"""
    print("🎥 Flowkar RTMP URL Generation Example")
    print("=" * 50)
    
    # Example user and room
    user_id = "12345"
    room_id = f"live-{user_id}"
    
    print(f"\n1. Getting RTMP server status...")
    get_rtmp_status()
    
    print(f"\n2. Generating RTMP URL for user {user_id}, room {room_id}...")
    rtmp_info = generate_rtmp_url(user_id, room_id)
    
    if rtmp_info:
        print(f"\n3. Getting stream info for user {user_id}...")
        get_stream_info(user_id)
        
        print(f"\n4. How to use this RTMP URL:")
        print(f"   📹 In OBS Studio:")
        print(f"      - Server: rtmp://{rtmp_info['server_host']}:{rtmp_info['server_port']}/{rtmp_info['application']}")
        print(f"      - Stream Key: {rtmp_info['stream_key']}")
        print(f"   📱 In mobile apps:")
        print(f"      - Full RTMP URL: {rtmp_info['rtmp_url']}")
        print(f"   🎬 For viewers (HLS): {rtmp_info['hls_url']}")
        
        print(f"\n5. Stopping stream for user {user_id}...")
        stop_stream(user_id)
    
    print(f"\n✨ Example completed!")

if __name__ == "__main__":
    main()
